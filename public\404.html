<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(to bottom, #0a0a1a, #1a1a2e);
            color: white;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(to right, #4f46e5, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }
        p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }
        a {
            display: inline-block;
            background: linear-gradient(to right, #4f46e5, #8b5cf6, #ec4899);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        a:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.5);
        }
    </style>
</head>
<body>
    <h1>404</h1>
    <p>Oops! The page you're looking for doesn't exist.</p>
    <a href="/">Return to Home</a>

    <script>
        // For Cloudflare Pages, we need to handle SPA routing
        document.addEventListener('DOMContentLoaded', function() {
            // Get the hostname to determine which platform we're on
            const hostname = window.location.hostname;
            const isGitHubPages = hostname === 'maxingkingvfx.github.io';
            const isCloudflarePages = hostname.includes('pages.dev');

            // Get the link element
            const homeLink = document.querySelector('a');

            // Set the correct home URL based on the platform
            if (isGitHubPages) {
                homeLink.href = '/portfolio/';
            } else {
                homeLink.href = '/';
            }

            // For Cloudflare Pages, we need to handle hash-based routing
            if (isCloudflarePages) {
                // Redirect to the home page after a short delay
                setTimeout(function() {
                    window.location.href = '/';
                }, 1500);
            }
        });
    </script>
</body>
</html>