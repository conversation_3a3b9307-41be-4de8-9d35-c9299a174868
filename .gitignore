# Dependencies
node_modules/
.bun/

# Build output
dist/
build/

# Environment variables
.env
.env.local
.env.*.local
.env.production
.env.development

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS
.DS_Store
Thumbs.db

# Cache
.cache/
node_modules/.cache/

# Temp files
temp.*
*.tmp
*.temp

# Cloudflare Worker files
cf-worker-n8n-proxy/

# Test files
test-session-id.html