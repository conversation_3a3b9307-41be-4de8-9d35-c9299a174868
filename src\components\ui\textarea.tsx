import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    // Create a ref for auto-resizing
    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null);

    // Combine the forwarded ref with our local ref
    const setRefs = React.useCallback(
      (element: HTMLTextAreaElement | null) => {
        // Update the local ref
        textareaRef.current = element;

        // Forward the ref
        if (typeof ref === 'function') {
          ref(element);
        } else if (ref) {
          ref.current = element;
        }
      },
      [ref]
    );

    // Auto-resize function
    const autoResize = React.useCallback(() => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';

      // Set the height to match content (with min/max constraints applied by CSS)
      textarea.style.height = `${textarea.scrollHeight}px`;
    }, []);

    // Auto-resize on content change
    React.useEffect(() => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      // Initial resize
      autoResize();

      // Add event listeners
      textarea.addEventListener('input', autoResize);

      // Cleanup
      return () => {
        textarea.removeEventListener('input', autoResize);
      };
    }, [autoResize]);

    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 overflow-hidden",
          className
        )}
        ref={setRefs}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
