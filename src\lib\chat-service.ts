/**
 * Service for handling chat interactions with n8n webhook
 */
import { CHAT_HEADERS, DEMO_MODE, DEMO_RESPONSES, ANALYTICS_WEBHOOK_URL } from '@/config/chat-config';

/**
 * Configuration for the chat service
 */
export interface ChatServiceConfig {
  webhookUrl: string;
  headers?: Record<string, string>;
}

/**
 * Message object structure
 */
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

/**
 * Chat service for interacting with n8n webhook
 */
export class ChatService {
  private config: ChatServiceConfig;

  constructor(config: ChatServiceConfig) {
    this.config = {
      headers: CHAT_HEADERS,
      ...config,
    };
  }

  /**
   * Send a message to the n8n webhook or generate a demo response
   * @param message The message to send
   * @param sessionId Optional session ID for conversation context
   * @returns Promise with the response from n8n or a demo response
   */
  async sendMessage(message: string, sessionId?: string): Promise<ChatMessage> {
    // If in demo mode, generate a simulated response
    if (DEMO_MODE) {
      return this.generateDemoResponse(message);
    }

    // Only send to analytics webhook when explicitly sending a message
    // This is only called when a user clicks the send button or presses Enter
    if (message && message.trim().length > 0) {
      this.sendToAnalyticsWebhook(message, sessionId);
    }

    // Send to main n8n webhook only if there's a valid message
    if (!message || message.trim().length === 0) {
      return {
        id: crypto.randomUUID(),
        content: 'Please enter a message.',
        sender: 'bot',
        timestamp: new Date(),
      };
    }

    // Log the parameters being sent
    console.log('Sending to webhook:', { message, sessionId });

    try {
      // Create an AbortController to handle timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 20000); // 20 second timeout

      try {
        const response = await fetch(this.config.webhookUrl, {
          method: 'POST',
          headers: this.config.headers,
          // We're not using no-cors mode for the main webhook because it makes the response opaque
          // and we need to read the JSON response
          body: JSON.stringify({
            message,
            sessionId,
            action: 'sendMessage',
          }),
          signal: controller.signal // Add abort signal for timeout
        });

        // Clear the timeout as soon as we get a response
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        return {
          id: crypto.randomUUID(),
          content: data.response || 'Sorry, I couldn\'t process your request.',
          sender: 'bot',
          timestamp: new Date(),
        };
      } finally {
        // Ensure timeout is cleared in all cases
        clearTimeout(timeoutId);
      }
    } catch (error) {
      console.error('Error sending message to n8n:', error);

      // Check if it's an abort error (timeout)
      if (error instanceof DOMException && error.name === 'AbortError') {
        return {
          id: crypto.randomUUID(),
          content: 'The request timed out. The assistant service might be temporarily unavailable. Please try again later.',
          sender: 'bot',
          timestamp: new Date(),
        };
      }

      // Check if it's a CORS error (TypeError with 'Failed to fetch' message)
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        return {
          id: crypto.randomUUID(),
          content: 'Unable to connect to the assistant service due to a CORS error. This typically happens during local development.',
          sender: 'bot',
          timestamp: new Date(),
        };
      }

      // For other errors, throw to be handled by the component
      throw error;
    }
  }

  /**
   * Send message data to the analytics webhook for monitoring
   * This is a fire-and-forget operation that doesn't affect the main chat flow
   * @param message The message to send
   * @param sessionId Optional session ID for context
   */
  private sendToAnalyticsWebhook(message: string, sessionId?: string): void {
    // Don't send to analytics webhook in demo mode
    if (DEMO_MODE) return;

    // Create an AbortController to handle timeouts
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    // Send to analytics webhook without waiting for response
    // Always use 'no-cors' mode for the analytics webhook to avoid CORS issues
    // This is a fire-and-forget operation, so we don't need to read the response
    fetch(ANALYTICS_WEBHOOK_URL, {
      method: 'POST',
      mode: 'no-cors', // This prevents CORS errors but makes the response opaque
      headers: {
        'Content-Type': 'application/json',
        // Use the same headers as the main webhook to ensure API key authentication
        ...this.config.headers
      },
      body: JSON.stringify({
        message,
        sessionId,
        timestamp: new Date().toISOString(),
        source: 'MaxingKing-Website-Chat',
        analytics: true
      }),
      signal: controller.signal // Add abort signal for timeout
    })
    .then(() => {
      clearTimeout(timeoutId);
      console.log('Analytics webhook request sent successfully');
      // Can't read response details in no-cors mode
      // Don't try to parse JSON as it will fail
    })
    .catch(err => {
      clearTimeout(timeoutId);
      // Check if this was an abort error (timeout)
      if (err instanceof DOMException && err.name === 'AbortError') {
        console.warn('Analytics webhook request timed out');
      } else {
        // Just log the error but don't affect the main chat flow
        console.warn('Error sending to analytics webhook:', err);
      }
      // No need to handle this error further as it's just for analytics
      // and doesn't affect the main chat functionality
    });
  }

  /**
   * Generate a demo response based on the user's message
   * @param message The user's message
   * @returns A simulated chat message response
   */
  private generateDemoResponse(message: string): Promise<ChatMessage> {
    return new Promise((resolve) => {
      // Simulate network delay
      setTimeout(() => {
        const lowerMessage = message.toLowerCase();
        let responseCategory = 'default';

        // Determine which category of responses to use based on keywords
        if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('rate') || lowerMessage.includes('charge')) {
          responseCategory = 'pricing';
        } else if (lowerMessage.includes('portfolio') || lowerMessage.includes('work') || lowerMessage.includes('example') || lowerMessage.includes('project')) {
          responseCategory = 'portfolio';
        } else if (lowerMessage.includes('contact') || lowerMessage.includes('email') || lowerMessage.includes('reach') || lowerMessage.includes('discord') || lowerMessage.includes('twitter')) {
          responseCategory = 'contact';
        } else if (lowerMessage.includes('time') || lowerMessage.includes('long') || lowerMessage.includes('deadline') || lowerMessage.includes('turnaround')) {
          responseCategory = 'turnaround';
        } else if (lowerMessage.includes('format') || lowerMessage.includes('bold') || lowerMessage.includes('italic') || lowerMessage.includes('list') || lowerMessage.includes('markdown')) {
          responseCategory = 'formatting';
        }

        // Get responses for the selected category
        const responses = DEMO_RESPONSES[responseCategory] || DEMO_RESPONSES.default;

        // Select a random response from the category
        const randomIndex = Math.floor(Math.random() * responses.length);
        const responseContent = responses[randomIndex];

        resolve({
          id: crypto.randomUUID(),
          content: responseContent,
          sender: 'bot',
          timestamp: new Date(),
        });
      }, 1000); // Simulate a 1-second delay
    });
  }


}


