{"compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "incremental": true, "target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@public/*": ["./public/*"]}, "types": ["vite/client", "node"], "strict": true, "skipLibCheck": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "jsx": "react-jsx", "allowJs": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "outDir": "./dist", "sourceMap": true, "inlineSources": true}, "include": ["src", "public", "*.config.ts", "vite.config.ts", "components.json"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts", "**/__mocks__/*"]}