/* Chat message formatting styles */

/* General message content */
.chat-message-content {
  line-height: 1.5;
  word-break: break-word;
  width: 100%;
  overflow-wrap: break-word;
}

/* Bot message specific styles */
.chat-message-bot {
  color: var(--foreground);
}

/* User message specific styles */
.chat-message-user {
  color: white;
}

/* Formatting styles */
.chat-message-content strong,
.chat-message-content b {
  font-weight: 700;
}

.chat-message-content em,
.chat-message-content i {
  font-style: italic;
}

/* Lists */
.chat-message-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.chat-message-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.chat-message-content li {
  margin-bottom: 0.25rem;
}

/* Line breaks and paragraphs */
.chat-message-content p {
  margin-bottom: 0.5rem;
  width: 100%;
  display: inline-block;
}

.chat-message-content p:last-child {
  margin-bottom: 0;
}

/* Links */
.chat-message-content a {
  color: var(--brand-light-purple);
  text-decoration: underline;
}

.chat-message-user a {
  color: white;
  text-decoration: underline;
}

/* Code blocks */
.chat-message-content code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.9em;
}

.chat-message-user code {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Blockquotes */
.chat-message-content blockquote {
  border-left: 3px solid var(--brand-light-purple);
  padding-left: 0.75rem;
  margin: 0.5rem 0;
  font-style: italic;
}

.chat-message-user blockquote {
  border-left-color: rgba(255, 255, 255, 0.5);
}

/* Custom scrollbar for chat area */
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(124, 58, 237, 0.3);
  border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(124, 58, 237, 0.5);
}

/* Firefox scrollbar styles for chat area */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(124, 58, 237, 0.3) transparent;
}

/* Chat input styles */
textarea.whitespace-pre-wrap {
  white-space: pre-wrap !important;
}

/* Ensure all text content stays within message boxes */
.chat-message-content * {
  max-width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Fix for text outside message boxes */
.chat-message-content {
  display: inline-block;
  width: 100%;
  white-space: normal;
  overflow-wrap: break-word;
}

/* Ensure text doesn't overflow */
.flex-col > .chat-message-content {
  width: 100%;
  box-sizing: border-box;
  overflow-x: visible;
}

/* Make sure long words break properly */
.chat-message-content p {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* Handle long words and URLs */
.chat-message-content * {
  max-width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}

textarea.word-break-word {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

/* Ensure the textarea expands properly */
.flex-1 > textarea {
  width: 100%;
  box-sizing: border-box;
  line-height: 1.5;
}

/* Custom scrollbar for textarea input - matching chat area scrollbar */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: transparent;
}

textarea::-webkit-scrollbar-thumb {
  background-color: rgba(124, 58, 237, 0.3);
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background-color: rgba(124, 58, 237, 0.5);
}

/* Firefox scrollbar styles */
textarea {
  scrollbar-width: thin;
  scrollbar-color: rgba(124, 58, 237, 0.3) transparent;
}

/* Question cards styles */
.question-card {
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, var(--brand-blue), var(--brand-purple));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.question-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, var(--brand-light-purple), var(--brand-purple));
}

.question-card:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Add a subtle glow effect to question cards */
.question-card {
  position: relative;
  overflow: hidden;
}

.question-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.question-card:hover::after {
  opacity: 1;
}
