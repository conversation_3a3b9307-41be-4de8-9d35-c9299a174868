import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

export interface QuestionCard {
  id: string;
  text: string;
}

interface QuestionCardsProps {
  questions: QuestionCard[];
  onQuestionClick: (question: string) => void;
}

/**
 * Component for displaying prebuilt question cards
 */
const QuestionCards: React.FC<QuestionCardsProps> = ({ questions, onQuestionClick }) => {
  return (
    <div className="flex flex-wrap gap-2 justify-center mb-3 px-2 py-1">
      {questions.map((question, index) => (
        <motion.button
          key={question.id}
          onClick={() => onQuestionClick(question.text)}
          className={cn(
            "py-1.5 px-3 rounded-full text-xs font-medium",
            "text-white",
            "hover:shadow-md hover:scale-105 active:scale-95",
            "transition-all duration-200 ease-in-out",
            "flex-shrink-0 max-w-[150px] truncate",
            "question-card"
          )}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.3,
            delay: index * 0.1,
            type: 'spring',
            stiffness: 300
          }}
        >
          {question.text}
        </motion.button>
      ))}
    </div>
  );
};

export default QuestionCards;
