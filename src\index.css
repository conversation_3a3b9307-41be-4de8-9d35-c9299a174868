/* stylelint-disable at-rule-no-unknown */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&display=swap');
@import './styles/chat.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 33% 11%;
    --foreground: 0 0% 98%;

    --card: 240 33% 11%;
    --card-foreground: 0 0% 98%;

    --popover: 240 33% 11%;
    --popover-foreground: 0 0% 98%;

    --primary: 252 84% 61%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 20% 18%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 20% 18%;
    --muted-foreground: 240 10% 70%;

    --accent: 240 20% 18%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 20% 20%;
    --input: 240 20% 18%;
    --ring: 252 84% 61%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    scroll-behavior: smooth;
    font-family: 'Poppins', sans-serif;
    color: #ffffff;
    background: linear-gradient(135deg, #0c0c1d 0%, #13132b 25%, #0f0f20 50%, #0a0a18 75%, #080810 100%);
    background-attachment: fixed;
    background-size: 400% 400%;
    animation: gradientBackground 30s ease infinite;
  }

  @keyframes gradientBackground {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    @apply bg-gradient-to-r from-brand-blue via-brand-light-purple to-brand-purple bg-clip-text text-transparent;
  }

  .gradient-button {
    @apply bg-gradient-to-r from-brand-blue via-brand-light-purple to-brand-purple hover:opacity-90 transition-opacity;
  }

  .card-hover {
    @apply transition-transform duration-300 hover:scale-105;
  }

  .service-card {
    @apply bg-[#0c0c1d] p-6 rounded-lg border border-gray-800 hover:border-brand-purple transition-all duration-300;
  }

  /* Reset headings */
  h1, h2, h3 {
    text-shadow: none;
  }

  /* Reset links */
  a {
    text-shadow: none;
    transition: none;
  }

  a:hover {
    text-shadow: none;
  }

  /* Reset buttons */
  button {
    box-shadow: none;
    transition: none;
  }

  button:hover {
    box-shadow: none;
  }

  .enhance-text {
    font-family: 'Orbitron', sans-serif;
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5),
                 0 0 20px rgba(139, 92, 246, 0.3),
                 0 0 30px rgba(139, 92, 246, 0.2);
    background: linear-gradient(to right, #60a5fa, #7c3aed);
    -webkit-background-clip: text;
    color: transparent;
    transition: text-shadow 0.3s ease;
  }

  .enhance-text:hover {
    text-shadow: 0 0 15px rgba(139, 92, 246, 0.7),
                 0 0 25px rgba(139, 92, 246, 0.5),
                 0 0 35px rgba(139, 92, 246, 0.3);
  }

  .glow-heading {
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5),
                 0 0 20px rgba(139, 92, 246, 0.3),
                 0 0 30px rgba(139, 92, 246, 0.2);
    background: linear-gradient(to right, #60a5fa, #7c3aed);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Animation classes */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-slide-in {
    animation: slideInRight 0.6s ease-out forwards;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-125;
  }

  .hover-glow {
    @apply transition-all hover:shadow-lg hover:shadow-purple-500/20;
  }

  .animate-gradient-x {
    animation: gradient-x 3s ease infinite;
    background-size: 200% 200%;
  }
}

/* Project Card Glow Effect */
.project-card-wrapper {
  position: relative; /* Needed for absolute positioning of pseudo-element */
  border-radius: 0.5rem; /* Match card rounding */
  overflow: visible; /* Allow shadow to extend beyond bounds */
}

.project-card-wrapper::before {
  content: '';
  position: absolute;
  inset: 0; /* Cover the entire wrapper */
  border-radius: inherit; /* Inherit border-radius */
  box-shadow: 0 0 0 0 rgba(120, 0, 255, 0); /* Initial transparent shadow */
  transition: box-shadow 0.3s ease-in-out;
  z-index: -1; /* Place behind the card content */
  pointer-events: none; /* Don't interfere with clicks */
}

/* Apply glow only if the wrapper does NOT have the 'is-short-card' class */
.project-card-wrapper:not(.is-short-card):hover::before {
  box-shadow: 0 8px 32px 0 rgba(120, 0, 255, 0.25); /* Purple glow on hover */
}

/* Description container styling */
.description-container {
  position: relative;
  transform-origin: left center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.description-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.1), transparent);
  opacity: 0.5;
  z-index: -1;
}

/* Section background overlays */
.section-overlay-dark {
  position: relative;
}

.section-overlay-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
  z-index: -1;
}

.section-overlay-light {
  position: relative;
}

.section-overlay-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(13, 13, 29, 0.3);
  backdrop-filter: blur(2px);
  z-index: -1;
}

/* Section transition gradients */
.section-transition-top {
  position: relative;
}

.section-transition-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.2) 40%, transparent);
  z-index: 0;
  pointer-events: none;
}

.section-transition-bottom {
  position: relative;
}

.section-transition-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.2) 40%, transparent);
  z-index: 0;
  pointer-events: none;
}

/* Custom scrollbar styles for chat */
.chat-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(124, 58, 237, 0.5) rgba(15, 15, 32, 0.3);
  /* Improve touch scrolling on mobile */
  -webkit-overflow-scrolling: touch;
  /* Indicate scrollable area */
  overscroll-behavior: contain;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 15, 32, 0.3);
  border-radius: 10px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #7c3aed, #4338ca);
  border-radius: 10px;
  border: 2px solid rgba(15, 15, 32, 0.3);
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #8b5cf6, #4f46e5);
}

/* Add a subtle animation to the description text */
@keyframes textShimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.description-container p {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
  position: relative;
}

.description-container p::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(139, 92, 246, 0.2) 50%,
    transparent 100%);
  animation: textShimmer 4s ease-in-out infinite;
}
